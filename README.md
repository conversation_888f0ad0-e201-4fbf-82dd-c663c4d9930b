# 🦷 LynnVill Dental Clinic Management System

[![Next.js](https://img.shields.io/badge/Next.js-15.3.4-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![Prisma](https://img.shields.io/badge/Prisma-6.10.1-2D3748?style=for-the-badge&logo=prisma)](https://www.prisma.io/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-4.0-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-336791?style=for-the-badge&logo=postgresql&logoColor=white)](https://www.postgresql.org/)

A comprehensive, production-ready dental clinic management system built with modern web technologies. This full-stack application provides complete functionality for managing patients, appointments, treatments, billing, inventory, and clinic operations with a focus on healthcare compliance and user experience.

## 🌟 Project Overview

This dental clinic management system is designed to streamline healthcare operations with a professional, accessible interface that serves multiple user roles. Built with Next.js 15 and TypeScript, it demonstrates advanced full-stack development skills including complex database design, authentication systems, payment integrations, and healthcare-specific features.

**Key Highlights:**
- 🏥 **Healthcare-focused UI/UX** with professional blue/white/soft green color schemes
- 🔐 **Multi-role RBAC** supporting patients, dentists, staff, and administrators
- 🇵🇭 **Philippine localization** with timezone support and local payment gateways
- 📱 **Responsive design** optimized for desktop, tablet, and mobile devices
- 🔒 **Security-first approach** with comprehensive audit logging and data protection

## ✨ Key Features

### 👥 **Multi-Role Authentication & Authorization**
- **Google Sign-in Integration** for seamless patient onboarding
- **Role-Based Access Control (RBAC)** with four distinct user types
- **JWT Authentication** with NextAuth.js for secure session management
- **Profile Management** with role-specific dashboards and permissions

### 📅 **Advanced Appointment Management**
- **Online Booking System** with real-time availability checking
- **Calendar Integration** supporting Google, Outlook, and Apple calendars
- **Philippine Timezone Support** (Asia/Manila, UTC+8) for accurate scheduling
- **Automated Notifications** via SMS (Twilio) and Email (SendGrid)
- **Appointment Status Tracking** with comprehensive workflow management

### 🏥 **Comprehensive Patient Management**
- **Digital Medical Records** with complete patient history tracking
- **Dental Chart Integration** for visual treatment documentation
- **File Management System** for secure document and image storage
- **Insurance Billing Support** with automated claim processing
- **Patient Portal** with appointment history and treatment summaries

### 👨‍⚕️ **Dentist-Facing Features**
- **Schedule Management** with working hours configuration and time blocking
- **Patient Records Access** with medical history timelines and treatment notes
- **Treatment Planning** with procedure tracking and progress monitoring
- **Revenue Analytics** with detailed reporting and insights
- **Inventory Integration** for tracking supplies and equipment usage

### 💰 **Payment & Billing System**
- **Multiple Payment Gateways**: Stripe, PayMongo, GCash, PayMaya
- **Philippine Peso (₱) Pricing** with realistic dental service rates
- **Automated Invoicing** with PDF generation and email delivery
- **Payment Tracking** with comprehensive financial reporting
- **Insurance Claims Processing** with automated workflows

### 🏢 **Multi-Branch Operations**
- **Branch Management** with location-specific configurations
- **Staff Assignment** across multiple clinic locations
- **Inventory Tracking** per branch with centralized reporting
- **Performance Analytics** with branch-wise comparisons

## 🛠️ Technology Stack

### **Frontend Technologies**
- **Next.js 15.3.4** - React framework with App Router and Server Actions
- **TypeScript 5.0** - Type-safe development with strict configuration
- **TailwindCSS v4** - Modern utility-first CSS framework
- **shadcn/ui** - High-quality, accessible React components
- **React Hook Form** - Performant forms with validation
- **Zod** - TypeScript-first schema validation
- **Lucide React** - Beautiful, customizable icons

### **Backend & Database**
- **Next.js API Routes** - RESTful API with server-side logic
- **Prisma ORM 6.10.1** - Type-safe database client with migrations
- **PostgreSQL** - Robust relational database with complex relationships
- **NextAuth.js 4.24.11** - Complete authentication solution
- **bcryptjs** - Secure password hashing
- **JWT** - Stateless authentication tokens

### **Third-Party Integrations**
- **Google OAuth** - Seamless patient authentication
- **Google Calendar API** - Calendar integration for appointments
- **Twilio** - SMS notifications and communications
- **SendGrid** - Email delivery and templates
- **Stripe** - International payment processing
- **PayMongo, GCash, PayMaya** - Philippine payment gateways

### **Development & Testing**
- **Jest 30.0.3** - Comprehensive testing framework
- **Testing Library** - React component testing utilities
- **Supertest** - API endpoint testing
- **ESLint** - Code quality and consistency
- **TypeScript Compiler** - Static type checking

## 🏗️ System Architecture

### **Database Schema**
The system uses a normalized PostgreSQL schema with 20+ interconnected models:

```
Users (Multi-role: Patient, Dentist, Staff, Admin)
├── PatientProfiles (Medical history, insurance, preferences)
├── DentistProfiles (Licenses, specializations, schedules)
├── StaffProfiles (Roles, permissions, branch assignments)
└── Appointments (Scheduling, status tracking, treatments)
    ├── Treatments (Procedures, notes, billing)
    ├── Invoices (Billing, payments, insurance claims)
    └── Files (Documents, images, medical records)
```

### **API Design**
RESTful API architecture with 30+ endpoints organized by domain:
- `/api/auth/*` - Authentication and session management
- `/api/appointments/*` - Appointment CRUD operations
- `/api/patients/*` - Patient management and medical records
- `/api/dentists/*` - Dentist profiles and schedules
- `/api/treatments/*` - Treatment records and procedures
- `/api/billing/*` - Invoicing and payment processing

### **Security Features**
- **Input Validation** with Zod schemas on all endpoints
- **Rate Limiting** to prevent abuse and ensure system stability
- **Audit Logging** for compliance and security monitoring
- **Role-Based Permissions** with middleware protection
- **Secure File Upload** with type validation and storage limits

## 📊 Performance Metrics

### **Build Performance**
- ✅ **Zero ESLint Warnings** - Clean, maintainable codebase
- ✅ **Zero TypeScript Errors** - Type-safe development with strict configuration
- ⚡ **57s Build Time** - Optimized production build with Prisma generation
- 📦 **102kB Shared Bundle** - Efficient code splitting and optimization

### **Application Architecture**
- 🌐 **52 Static Routes** - Pre-rendered pages for optimal performance
- 🔄 **47 Dynamic API Endpoints** - RESTful API with comprehensive coverage
- 🎯 **72.9kB Middleware** - Authentication and authorization layer
- 📱 **Mobile-First Design** - Responsive across all device sizes

### **Page Performance Analysis**
| Route | Bundle Size | First Load JS | Type |
|-------|-------------|---------------|------|
| `/` (Landing) | 12.1 kB | 163 kB | Static |
| `/dentist/schedule` | 46.2 kB | 231 kB | Static |
| `/dentist/appointments` | 25.0 kB | 218 kB | Static |
| `/dentist/patients` | 19.7 kB | 219 kB | Static |
| `/appointments/book` | 14.9 kB | 167 kB | Static |

### **Testing Coverage**
- ✅ **74 Passing Tests** across 4 comprehensive test suites
- 🧪 **3.767s Test Runtime** - Fast feedback loop for development
- 🔍 **API Endpoint Testing** - Complete validation and error handling
- 🛡️ **Authentication Testing** - Security and authorization coverage
- 📝 **Schema Validation Testing** - Data integrity and type safety

### **Database Performance**
- 🗄️ **20+ Normalized Models** - Efficient PostgreSQL schema design
- 🔗 **Proper Foreign Key Relationships** - Data integrity and consistency
- 📈 **Optimized Query Patterns** - Prisma ORM with connection pooling
- 🔍 **Efficient Indexing Strategy** - Fast data retrieval and searching

### **Code Quality Metrics**
- 📏 **TypeScript Strict Mode** - Maximum type safety and error prevention
- 🎯 **Component Architecture** - Reusable, maintainable UI components
- 🔧 **Custom Hooks** - Business logic separation and reusability
- 📚 **Comprehensive Documentation** - API docs and implementation guides

## 🏗️ System Architecture

The LynnVill Dental Clinic Management System follows a modern, layered architecture designed for scalability, maintainability, and security:

```mermaid
graph TB
    %% User Layer
    subgraph "👥 User Layer"
        P[👤 Patients<br/>Google OAuth]
        D[👨‍⚕️ Dentists<br/>Credentials Auth]
        A[👨‍💼 Admins<br/>Role-based Access]
    end

    %% Frontend Layer
    subgraph "🎨 Frontend Layer"
        UI[📱 Next.js 15 App<br/>TypeScript + TailwindCSS<br/>shadcn/ui Components]
        RHF[📝 React Hook Form<br/>Zod Validation]
        AUTH[🔐 NextAuth.js<br/>JWT Sessions]
    end

    %% API Layer
    subgraph "🔄 API Layer"
        MW[🛡️ Middleware<br/>Auth + Rate Limiting]
        API[🌐 REST API<br/>47 Endpoints]
        VAL[✅ Input Validation<br/>Zod Schemas]
    end

    %% Business Logic Layer
    subgraph "🧠 Business Logic"
        APPT[📅 Appointment<br/>Management]
        PAT[🏥 Patient<br/>Records]
        SCHED[⏰ Schedule<br/>Management]
        BILL[💰 Billing &<br/>Payments]
        INV[📦 Inventory<br/>Management]
    end

    %% Data Layer
    subgraph "🗄️ Data Layer"
        PRISMA[⚡ Prisma ORM<br/>Type-safe Queries]
        DB[(🐘 PostgreSQL<br/>20+ Models<br/>Normalized Schema)]
        AUDIT[📋 Audit Logs<br/>Compliance Tracking]
    end

    %% External Services
    subgraph "🌐 External Services"
        GOOGLE[📧 Google OAuth<br/>Calendar Integration]
        TWILIO[📱 Twilio SMS<br/>Notifications]
        SENDGRID[✉️ SendGrid Email<br/>Communications]
        STRIPE[💳 Payment Gateways<br/>Stripe, PayMongo, GCash]
    end

    %% File Storage
    subgraph "📁 File Storage"
        FILES[🗂️ Secure File Upload<br/>Medical Records<br/>Patient Documents]
    end

    %% Connections
    P --> UI
    D --> UI
    A --> UI

    UI --> RHF
    UI --> AUTH
    AUTH --> MW
    RHF --> MW

    MW --> API
    API --> VAL
    VAL --> APPT
    VAL --> PAT
    VAL --> SCHED
    VAL --> BILL
    VAL --> INV

    APPT --> PRISMA
    PAT --> PRISMA
    SCHED --> PRISMA
    BILL --> PRISMA
    INV --> PRISMA

    PRISMA --> DB
    PRISMA --> AUDIT

    API --> GOOGLE
    API --> TWILIO
    API --> SENDGRID
    API --> STRIPE
    API --> FILES

    %% Styling
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef frontendClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef apiClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef businessClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef externalClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef fileClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px

    class P,D,A userClass
    class UI,RHF,AUTH frontendClass
    class MW,API,VAL apiClass
    class APPT,PAT,SCHED,BILL,INV businessClass
    class PRISMA,DB,AUDIT dataClass
    class GOOGLE,TWILIO,SENDGRID,STRIPE externalClass
    class FILES fileClass
```

### **Architecture Highlights**
- 🏗️ **Layered Architecture** - Clear separation of concerns across 7 distinct layers
- 🔐 **Security-First Design** - Authentication and authorization at every layer
- 🌐 **RESTful API Design** - 47 endpoints with comprehensive validation
- 🗄️ **Normalized Database** - 20+ models with proper relationships
- 🔌 **External Integrations** - Payment gateways, communication services, and calendar APIs
- 📱 **Modern Frontend** - React 18+ with TypeScript and component-based architecture

## 🚀 Installation & Setup

### **Prerequisites**
- Node.js 18+ (LTS recommended)
- PostgreSQL 13+ (local or cloud instance)
- npm or yarn package manager
- Google Cloud Console account (for OAuth)

### **1. Clone and Install Dependencies**

```bash
git clone https://github.com/your-username/dental-clinic-management.git
cd dental-clinic-management
npm install
```

### **2. Environment Configuration**

Create environment file from template:
```bash
cp .env.example .env
```

Configure your `.env` file with the following variables:

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/dental_clinic"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-oauth-client-id"
GOOGLE_CLIENT_SECRET="your-google-oauth-client-secret"

# JWT Configuration
JWT_SECRET="your-jwt-secret-key-here"

# Email Service (SendGrid)
SENDGRID_API_KEY="your-sendgrid-api-key"
SENDGRID_FROM_EMAIL="<EMAIL>"

# SMS Service (Twilio)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="your-twilio-phone-number"

# Payment Gateways
STRIPE_SECRET_KEY="your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="your-stripe-publishable-key"
PAYMONGO_SECRET_KEY="your-paymongo-secret-key"
GCASH_MERCHANT_ID="your-gcash-merchant-id"
PAYMAYA_SECRET_KEY="your-paymaya-secret-key"

# File Storage (Optional)
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"
```

### **3. Database Setup**

Initialize Prisma and generate client:
```bash
npx prisma generate
npx prisma db push
```

Seed the database with sample data:
```bash
npm run db:seed
```

### **4. Start Development Server**

```bash
npm run dev
```

Visit `http://localhost:3000` to access the application.

## 📋 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `DATABASE_URL` | PostgreSQL connection string | ✅ |
| `NEXTAUTH_URL` | Application base URL | ✅ |
| `NEXTAUTH_SECRET` | NextAuth.js secret key | ✅ |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | ✅ |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | ✅ |
| `JWT_SECRET` | JWT signing secret | ✅ |
| `SENDGRID_API_KEY` | SendGrid API key for emails | ⚠️ |
| `TWILIO_ACCOUNT_SID` | Twilio account SID for SMS | ⚠️ |
| `TWILIO_AUTH_TOKEN` | Twilio auth token | ⚠️ |
| `STRIPE_SECRET_KEY` | Stripe payment processing | ⚠️ |
| `PAYMONGO_SECRET_KEY` | PayMongo payment gateway | ⚠️ |

**Legend:** ✅ Required | ⚠️ Optional (for full functionality)

## 🗄️ Database Setup & Seeding

The application includes comprehensive database seeding with realistic Philippine dental clinic data:

### **Seeded Data Includes:**
- **11 User Accounts** across all roles (Admin, Staff, Dentists, Patients)
- **3 Dentist Profiles** with specializations and schedules
- **5 Patient Profiles** with medical histories and Google OAuth compatibility
- **15 Dental Services** with Philippine Peso (₱) pricing
- **Multiple Appointments** in various states (scheduled, completed, cancelled)
- **Treatment Records** with realistic dental procedures
- **Invoice Data** with payment tracking
- **Inventory Items** for dental supplies and equipment

### **Database Commands:**
```bash
# Generate Prisma client
npm run db:generate

# Reset database and reseed
npm run db:reset

# Seed database only
npm run db:seed
```

## 🧪 Development Workflow

This project follows a systematic Quality Assurance (QA) workflow to ensure code quality and reliability:

### **QA Workflow (Execute in Order):**
```bash
# 1. Lint code for style and potential issues
npm run lint

# 2. Type-check TypeScript code
npm run typecheck

# 3. Build production bundle
npm run build

# 4. Run comprehensive test suite
npm run test
```

### **Additional Scripts:**
```bash
# Development with Turbopack
npm run dev

# Start production server
npm run start

# Watch mode testing
npm run test:watch

# Test coverage report
npm run test:coverage
```

## 📱 Screenshots & Demo

> **Note:** Add screenshots of key interfaces here for portfolio presentation

### **Patient Dashboard**
![Patient Dashboard](./docs/screenshots/patient-dashboard.png)
*Clean, healthcare-focused interface with appointment booking and medical history access*

### **Dentist Schedule Management**
![Dentist Schedule](./docs/screenshots/dentist-schedule.png)
*Comprehensive schedule management with time blocking and appointment integration*

### **Appointment Booking Flow**
![Appointment Booking](./docs/screenshots/appointment-booking.png)
*Intuitive booking process with real-time availability and service selection*

### **Admin Analytics Dashboard**
![Admin Dashboard](./docs/screenshots/admin-dashboard.png)
*Business intelligence with revenue tracking and operational insights*

## 🔒 Security Features

### **Authentication & Authorization**
- **Multi-factor Authentication** with email/SMS verification
- **Session Management** with secure JWT tokens and refresh mechanisms
- **Role-Based Access Control** with granular permissions
- **OAuth Integration** with Google for patient convenience

### **Data Protection**
- **Input Sanitization** with Zod schema validation
- **SQL Injection Prevention** through Prisma ORM parameterized queries
- **XSS Protection** with Content Security Policy headers
- **Rate Limiting** to prevent abuse and ensure system stability

### **Compliance & Auditing**
- **Comprehensive Audit Logging** for all user actions
- **Data Encryption** for sensitive patient information
- **HIPAA-Ready Architecture** with proper data handling procedures
- **Secure File Storage** with access controls and virus scanning

## 🚀 Deployment

### **Production Deployment Options**

#### **Vercel (Recommended)**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to Vercel
vercel --prod
```

#### **Docker Deployment**
```dockerfile
# Dockerfile included for containerized deployment
docker build -t dental-clinic .
docker run -p 3000:3000 dental-clinic
```

### **Environment Setup for Production**
- Configure production database (Neon.tech, AWS RDS, etc.)
- Set up proper environment variables in deployment platform
- Configure domain and SSL certificates
- Set up monitoring and logging services
- Configure backup and disaster recovery procedures

## 🤝 Contributing

We welcome contributions to improve the dental clinic management system! Please follow these guidelines:

### **Development Setup**
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Follow the QA workflow: `lint → typecheck → build → test`
4. Commit changes: `git commit -m 'Add amazing feature'`
5. Push to branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

### **Code Standards**
- Follow TypeScript strict mode guidelines
- Use ESLint configuration for code style
- Write comprehensive tests for new features
- Update documentation for API changes
- Follow healthcare data handling best practices

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **shadcn/ui** for the beautiful, accessible component library
- **Prisma** for the excellent database toolkit and ORM
- **NextAuth.js** for comprehensive authentication solutions
- **Vercel** for seamless deployment and hosting platform
- **Healthcare Community** for insights into dental practice management needs

---

**Built with ❤️ for modern healthcare practices**

*This project demonstrates advanced full-stack development skills including complex database design, multi-role authentication, payment processing, healthcare compliance, and modern web development best practices.*
